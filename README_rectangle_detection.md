# OpenCV 矩形检测程序

本项目包含了多个使用OpenCV实现矩形检测的Python程序，适合不同水平的学习者使用。

## 文件说明

### 静态图像检测版本

### 1. `test1.py` - 基础版本
- **功能**: 最简单的矩形检测实现
- **适合**: OpenCV初学者
- **特点**: 代码简洁，易于理解基本流程

### 2. `simple_rectangle_detector.py` - 教学版本
- **功能**: 详细的步骤说明和交互式参数调整
- **适合**: 想要深入理解检测原理的学习者
- **特点**:
  - 详细的步骤输出
  - 交互式参数调整功能
  - 菜单式操作界面

### 3. `rectangle_detector.py` - 高级版本
- **功能**: 多种检测方法的完整实现
- **适合**: 有一定OpenCV基础的开发者
- **特点**:
  - 3种不同的检测算法
  - 面向对象的设计
  - 参数可配置

### 实时摄像头检测版本

### 4. `simple_camera_detector.py` - 简单摄像头版本
- **功能**: 实时摄像头矩形检测，显示FPS
- **适合**: 想要体验实时检测的初学者
- **特点**:
  - 简单易懂的代码结构
  - 实时FPS显示
  - 保存截图功能
  - 摄像头状态检测

### 5. `camera_rectangle_detector.py` - 完整摄像头版本
- **功能**: 功能完整的实时检测器
- **适合**: 需要完整功能的用户
- **特点**:
  - 实时参数调整
  - 多窗口显示
  - 性能优化
  - 详细的检测信息

### 6. `advanced_camera_detector.py` - 高性能版本
- **功能**: 高性能多线程实时检测
- **适合**: 对性能有要求的高级用户
- **特点**:
  - 多线程处理
  - 性能统计和历史图表
  - 自适应图像处理
  - 高级矩形验证算法

## 安装依赖

```bash
pip install opencv-python numpy
```

## 使用方法

### 快速开始

#### 静态图像检测
```bash
# 运行基础版本
python test1.py

# 运行教学版本（推荐）
python simple_rectangle_detector.py

# 运行高级版本
python rectangle_detector.py
```

#### 实时摄像头检测
```bash
# 运行简单摄像头版本（推荐新手）
python simple_camera_detector.py

# 运行完整摄像头版本
python camera_rectangle_detector.py

# 运行高性能版本
python advanced_camera_detector.py
```

### 准备图像
确保在 `image/` 目录下有测试图像文件（如 `car1.jpg`）

## 检测原理

### 基本流程
1. **图像预处理**: 灰度化、高斯模糊
2. **边缘检测**: 使用Canny算法
3. **轮廓查找**: 找到所有封闭轮廓
4. **形状分析**: 通过轮廓近似判断是否为矩形
5. **结果显示**: 标注检测到的矩形

### 关键参数

#### Canny边缘检测
- `低阈值`: 通常设为50-100
- `高阈值`: 通常设为150-200
- `高阈值 = 2-3 × 低阈值`

#### 轮廓筛选
- `最小面积`: 过滤噪声，建议1000+
- `最大面积`: 避免检测整个图像
- `近似精度`: 0.01-0.05，值越小越精确

#### 矩形判断
- `顶点数 = 4`: 近似后的多边形有4个顶点
- `凸性检查`: 确保是凸四边形
- `长宽比`: 可选的形状约束

## 检测方法对比

### 方法1: 轮廓近似
- **原理**: Canny边缘 → 轮廓查找 → 多边形近似
- **优点**: 精确度高，适合规则矩形
- **缺点**: 对噪声敏感

### 方法2: 霍夫直线变换
- **原理**: 检测直线 → 组合成矩形
- **优点**: 对部分遮挡鲁棒
- **缺点**: 计算复杂，可能产生误检

### 方法3: 形态学操作
- **原理**: 二值化 → 形态学处理 → 轮廓分析
- **优点**: 对光照变化鲁棒
- **缺点**: 可能丢失细节

## 参数调优建议

### 图像质量好的情况
```python
# Canny参数
low_threshold = 50
high_threshold = 150

# 面积阈值
min_area = 1000
max_area = 50000

# 近似精度
epsilon_factor = 0.02
```

### 图像噪声较多的情况
```python
# 增加模糊强度
blur_kernel = (7, 7)  # 原来是(5, 5)

# 调整Canny阈值
low_threshold = 30
high_threshold = 100

# 放宽面积限制
min_area = 500
```

### 矩形较小的情况
```python
# 降低面积阈值
min_area = 200

# 提高近似精度
epsilon_factor = 0.01
```

## 常见问题

### Q: 检测不到矩形？
A: 
1. 检查图像路径是否正确
2. 调低面积阈值
3. 调整Canny参数
4. 使用交互式调参功能

### Q: 检测到太多误报？
A: 
1. 提高面积阈值
2. 增加形状约束（长宽比等）
3. 调整近似精度

### Q: 矩形边缘不准确？
A: 
1. 降低近似精度参数
2. 改善图像预处理
3. 尝试不同的边缘检测参数

## 扩展功能

可以在现有代码基础上添加：
- 矩形角度检测
- 长宽比筛选
- 颜色信息利用
- 多尺度检测
- 实时视频检测

## 学习建议

1. **从简单开始**: 先运行 `test1.py` 理解基本流程
2. **参数实验**: 使用 `simple_rectangle_detector.py` 的交互模式
3. **深入理解**: 研究 `rectangle_detector.py` 的多种方法
4. **实际应用**: 尝试检测自己的图像

## 技术栈
- Python 3.x
- OpenCV 4.x
- NumPy

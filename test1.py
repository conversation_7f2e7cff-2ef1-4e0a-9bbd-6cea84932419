import cv2             #opencv读取格式是BGR
import numpy as np

def detect_rectangles(image_path):
    """
    检测图像中的矩形
    """
    # 读取原始图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return

    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 高斯模糊，减少噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # 边缘检测
    edges = cv2.Canny(blurred, 50, 150)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 创建结果图像副本
    result = img.copy()
    rectangle_count = 0

    # 遍历所有轮廓
    for contour in contours:
        # 计算轮廓面积，过滤小的轮廓
        area = cv2.contourArea(contour)
        if area < 1000:  # 面积阈值，可调整
            continue

        # 轮廓近似
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)

        # 如果近似轮廓有4个顶点，认为是矩形
        if len(approx) == 4:
            rectangle_count += 1

            # 绘制矩形轮廓
            cv2.drawContours(result, [approx], -1, (0, 255, 0), 3)

            # 计算矩形的边界框
            x, y, w, h = cv2.boundingRect(approx)

            # 在矩形上标注序号
            cv2.putText(result, f'Rect {rectangle_count}',
                       (x, y-10), cv2.FONT_HERSHEY_SIMPLEX,
                       0.7, (0, 255, 0), 2)

            print(f"矩形 {rectangle_count}: 位置({x}, {y}), 大小({w}x{h}), 面积: {area:.0f}")

    print(f"总共检测到 {rectangle_count} 个矩形")

    # 显示结果
    cv2.namedWindow('Original', cv2.WINDOW_NORMAL)
    cv2.namedWindow('Edges', cv2.WINDOW_NORMAL)
    cv2.namedWindow('Rectangle Detection', cv2.WINDOW_NORMAL)

    cv2.imshow('Original', img)
    cv2.imshow('Edges', edges)
    cv2.imshow('Rectangle Detection', result)

    cv2.waitKey(0)
    cv2.destroyAllWindows()

# 主程序
if __name__ == "__main__":
    # 检测矩形
    detect_rectangles('image/car1.jpg')

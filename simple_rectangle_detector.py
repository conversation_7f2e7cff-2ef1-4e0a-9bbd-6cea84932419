import cv2
import numpy as np

def simple_rectangle_detection(image_path):
    """
    简单的矩形检测函数
    适合初学者理解OpenCV矩形检测的基本流程
    """
    
    # 步骤1: 读取图像
    print("步骤1: 读取图像...")
    img = cv2.imread(image_path)
    if img is None:
        print(f"错误: 无法读取图像 {image_path}")
        return
    
    print(f"图像尺寸: {img.shape}")
    
    # 步骤2: 转换为灰度图
    print("步骤2: 转换为灰度图...")
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 步骤3: 高斯模糊去噪
    print("步骤3: 高斯模糊去噪...")
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 步骤4: 边缘检测
    print("步骤4: Canny边缘检测...")
    edges = cv2.Canny(blurred, 50, 150)
    
    # 步骤5: 查找轮廓
    print("步骤5: 查找轮廓...")
    contours, hierarchy = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    print(f"找到 {len(contours)} 个轮廓")
    
    # 步骤6: 筛选和检测矩形
    print("步骤6: 分析轮廓，检测矩形...")
    
    # 创建结果图像
    result = img.copy()
    rectangle_count = 0
    
    # 设置参数
    min_area = 1000  # 最小面积阈值
    max_area = 50000  # 最大面积阈值
    
    for i, contour in enumerate(contours):
        # 计算轮廓面积
        area = cv2.contourArea(contour)
        
        # 过滤太小或太大的轮廓
        if area < min_area or area > max_area:
            continue
        
        # 计算轮廓周长
        perimeter = cv2.arcLength(contour, True)
        
        # 轮廓近似 - 将轮廓近似为多边形
        epsilon = 0.02 * perimeter  # 近似精度
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 检查是否为矩形（4个顶点）
        if len(approx) == 4:
            rectangle_count += 1
            
            # 绘制矩形轮廓
            cv2.drawContours(result, [approx], -1, (0, 255, 0), 3)
            
            # 获取边界框信息
            x, y, w, h = cv2.boundingRect(approx)
            
            # 添加文字标注
            label = f'矩形{rectangle_count}'
            cv2.putText(result, label, (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 打印矩形信息
            print(f"  矩形 {rectangle_count}:")
            print(f"    位置: ({x}, {y})")
            print(f"    大小: {w} x {h}")
            print(f"    面积: {area:.0f}")
            print(f"    顶点数: {len(approx)}")
    
    print(f"\n总共检测到 {rectangle_count} 个矩形")
    
    # 步骤7: 显示结果
    print("步骤7: 显示检测结果...")
    
    # 创建窗口
    cv2.namedWindow('原始图像', cv2.WINDOW_NORMAL)
    cv2.namedWindow('灰度图像', cv2.WINDOW_NORMAL)
    cv2.namedWindow('边缘检测', cv2.WINDOW_NORMAL)
    cv2.namedWindow('矩形检测结果', cv2.WINDOW_NORMAL)
    
    # 显示图像
    cv2.imshow('原始图像', img)
    cv2.imshow('灰度图像', gray)
    cv2.imshow('边缘检测', edges)
    cv2.imshow('矩形检测结果', result)
    
    print("\n按任意键关闭所有窗口...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    return rectangle_count

def interactive_parameter_tuning(image_path):
    """
    交互式参数调整功能
    允许用户实时调整检测参数
    """
    print("=== 交互式参数调整模式 ===")
    print("使用滑动条调整参数，按 'q' 退出")
    
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return
    
    # 创建窗口和滑动条
    cv2.namedWindow('参数调整', cv2.WINDOW_NORMAL)
    cv2.namedWindow('检测结果', cv2.WINDOW_NORMAL)
    
    # 创建滑动条
    cv2.createTrackbar('Canny低阈值', '参数调整', 50, 200, lambda x: None)
    cv2.createTrackbar('Canny高阈值', '参数调整', 150, 300, lambda x: None)
    cv2.createTrackbar('最小面积/100', '参数调整', 10, 100, lambda x: None)
    cv2.createTrackbar('近似精度x100', '参数调整', 2, 10, lambda x: None)
    
    while True:
        # 获取滑动条值
        low_threshold = cv2.getTrackbarPos('Canny低阈值', '参数调整')
        high_threshold = cv2.getTrackbarPos('Canny高阈值', '参数调整')
        min_area = cv2.getTrackbarPos('最小面积/100', '参数调整') * 100
        epsilon_factor = cv2.getTrackbarPos('近似精度x100', '参数调整') / 100.0
        
        # 处理图像
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blurred, low_threshold, high_threshold)
        
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        result = img.copy()
        rectangle_count = 0
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < min_area:
                continue
                
            perimeter = cv2.arcLength(contour, True)
            epsilon = epsilon_factor * perimeter
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            if len(approx) == 4:
                rectangle_count += 1
                cv2.drawContours(result, [approx], -1, (0, 255, 0), 2)
        
        # 显示参数信息
        info_text = [
            f"Canny: {low_threshold}-{high_threshold}",
            f"最小面积: {min_area}",
            f"近似精度: {epsilon_factor:.2f}",
            f"检测到矩形: {rectangle_count}"
        ]
        
        for i, text in enumerate(info_text):
            cv2.putText(result, text, (10, 30 + i*25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow('参数调整', edges)
        cv2.imshow('检测结果', result)
        
        # 按 'q' 退出
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cv2.destroyAllWindows()

def main():
    """
    主函数 - 提供菜单选择不同的检测模式
    """
    image_path = 'image/car1.jpg'
    
    print("OpenCV 矩形检测程序")
    print("=" * 30)
    print("1. 简单矩形检测")
    print("2. 交互式参数调整")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择功能 (1-3): ").strip()
        
        if choice == '1':
            print("\n执行简单矩形检测...")
            count = simple_rectangle_detection(image_path)
            if count is not None:
                print(f"检测完成，共找到 {count} 个矩形")
        
        elif choice == '2':
            print("\n启动交互式参数调整...")
            interactive_parameter_tuning(image_path)
        
        elif choice == '3':
            print("程序退出")
            break
        
        else:
            print("无效选择，请输入 1-3")

if __name__ == "__main__":
    main()

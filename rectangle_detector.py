import cv2
import numpy as np
import os

class RectangleDetector:
    """
    矩形检测器类，提供多种矩形检测方法
    """
    
    def __init__(self):
        self.min_area = 1000  # 最小面积阈值
        self.max_area = 50000  # 最大面积阈值
        self.epsilon_factor = 0.02  # 轮廓近似因子
        
    def method1_contour_approximation(self, image_path):
        """
        方法1: 基于轮廓近似的矩形检测
        """
        print("=== 方法1: 轮廓近似检测 ===")
        
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            print(f"无法读取图像: {image_path}")
            return None
            
        # 预处理
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        result = img.copy()
        rectangle_count = 0
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_area <= area <= self.max_area:
                # 轮廓近似
                epsilon = self.epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # 检查是否为矩形（4个顶点）
                if len(approx) == 4:
                    rectangle_count += 1
                    cv2.drawContours(result, [approx], -1, (0, 255, 0), 2)
                    
                    # 标注
                    x, y, w, h = cv2.boundingRect(approx)
                    cv2.putText(result, f'R{rectangle_count}', (x, y-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        print(f"检测到 {rectangle_count} 个矩形")
        return result, edges
    
    def method2_hough_lines(self, image_path):
        """
        方法2: 基于霍夫直线变换的矩形检测
        """
        print("=== 方法2: 霍夫直线检测 ===")
        
        img = cv2.imread(image_path)
        if img is None:
            return None
            
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # 霍夫直线变换
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, 
                               minLineLength=50, maxLineGap=10)
        
        result = img.copy()
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                cv2.line(result, (x1, y1), (x2, y2), (255, 0, 0), 2)
        
        return result, edges
    
    def method3_template_matching(self, image_path):
        """
        方法3: 基于形态学操作的矩形检测
        """
        print("=== 方法3: 形态学操作检测 ===")
        
        img = cv2.imread(image_path)
        if img is None:
            return None
            
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 二值化
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        opening = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        closing = cv2.morphologyEx(opening, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(closing, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        result = img.copy()
        rectangle_count = 0
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > self.min_area:
                # 计算轮廓的凸包
                hull = cv2.convexHull(contour)
                hull_area = cv2.contourArea(hull)
                
                # 计算凸性比率
                if hull_area > 0:
                    solidity = area / hull_area
                    if solidity > 0.8:  # 凸性阈值
                        rectangle_count += 1
                        cv2.drawContours(result, [contour], -1, (0, 0, 255), 2)
        
        print(f"检测到 {rectangle_count} 个类矩形区域")
        return result, closing
    
    def detect_all_methods(self, image_path):
        """
        使用所有方法检测矩形
        """
        print(f"正在分析图像: {image_path}")
        print("=" * 50)
        
        # 方法1: 轮廓近似
        result1, edges1 = self.method1_contour_approximation(image_path)
        
        # 方法2: 霍夫直线
        result2, edges2 = self.method2_hough_lines(image_path)
        
        # 方法3: 形态学操作
        result3, binary3 = self.method3_template_matching(image_path)
        
        # 显示结果
        if result1 is not None:
            # 原图
            original = cv2.imread(image_path)
            
            # 创建窗口
            cv2.namedWindow('Original Image', cv2.WINDOW_NORMAL)
            cv2.namedWindow('Method 1: Contour Approximation', cv2.WINDOW_NORMAL)
            cv2.namedWindow('Method 2: Hough Lines', cv2.WINDOW_NORMAL)
            cv2.namedWindow('Method 3: Morphological', cv2.WINDOW_NORMAL)
            cv2.namedWindow('Edge Detection', cv2.WINDOW_NORMAL)
            
            # 显示图像
            cv2.imshow('Original Image', original)
            cv2.imshow('Method 1: Contour Approximation', result1)
            cv2.imshow('Method 2: Hough Lines', result2)
            cv2.imshow('Method 3: Morphological', result3)
            cv2.imshow('Edge Detection', edges1)
            
            print("\n按任意键关闭窗口...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()

def main():
    """
    主函数
    """
    # 创建检测器
    detector = RectangleDetector()
    
    # 设置参数
    detector.min_area = 500   # 最小面积
    detector.max_area = 100000  # 最大面积
    detector.epsilon_factor = 0.02  # 近似精度
    
    # 检测图像
    image_path = 'image/car1.jpg'
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        print("请确保图像文件路径正确")
        return
    
    # 执行检测
    detector.detect_all_methods(image_path)

if __name__ == "__main__":
    main()

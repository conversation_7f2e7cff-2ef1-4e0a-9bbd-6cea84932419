import cv2
import numpy as np
import time

class CameraRectangleDetector:
    """
    实时摄像头矩形检测器
    """
    
    def __init__(self, camera_id=0):
        """
        初始化摄像头和检测参数
        """
        self.camera_id = camera_id
        self.cap = None
        
        # 检测参数
        self.min_area = 1000
        self.max_area = 50000
        self.epsilon_factor = 0.02
        self.canny_low = 50
        self.canny_high = 150
        
        # 帧率计算
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
        # 性能优化
        self.frame_skip = 1  # 每隔几帧处理一次
        self.frame_count = 0
        
    def initialize_camera(self):
        """
        初始化摄像头
        """
        print("正在初始化摄像头...")
        self.cap = cv2.VideoCapture(self.camera_id)
        
        if not self.cap.isOpened():
            print(f"错误: 无法打开摄像头 {self.camera_id}")
            return False
        
        # 设置摄像头参数
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        self.cap.set(cv2.CAP_PROP_FPS, 30)
        
        print("摄像头初始化成功!")
        return True
    
    def calculate_fps(self):
        """
        计算帧率
        """
        self.fps_counter += 1
        current_time = time.time()
        
        # 每秒更新一次FPS
        if current_time - self.fps_start_time >= 1.0:
            self.current_fps = self.fps_counter / (current_time - self.fps_start_time)
            self.fps_counter = 0
            self.fps_start_time = current_time
    
    def detect_rectangles_in_frame(self, frame):
        """
        在单帧中检测矩形
        """
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 边缘检测
        edges = cv2.Canny(blurred, self.canny_low, self.canny_high)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 检测矩形
        rectangles = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            if self.min_area <= area <= self.max_area:
                # 轮廓近似
                epsilon = self.epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # 检查是否为矩形
                if len(approx) == 4:
                    rectangles.append(approx)
        
        return rectangles, edges
    
    def draw_rectangles(self, frame, rectangles):
        """
        在帧上绘制检测到的矩形
        """
        result = frame.copy()
        
        for i, rect in enumerate(rectangles):
            # 绘制矩形轮廓
            cv2.drawContours(result, [rect], -1, (0, 255, 0), 2)
            
            # 获取边界框
            x, y, w, h = cv2.boundingRect(rect)
            
            # 添加标签
            label = f'Rect {i+1}'
            cv2.putText(result, label, (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # 显示面积信息
            area = cv2.contourArea(rect)
            area_text = f'Area: {int(area)}'
            cv2.putText(result, area_text, (x, y+h+20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        
        return result
    
    def draw_info_overlay(self, frame, rectangle_count):
        """
        绘制信息覆盖层（FPS、参数等）
        """
        height, width = frame.shape[:2]
        
        # 创建半透明背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (300, 120), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # 显示信息
        info_texts = [
            f'FPS: {self.current_fps:.1f}',
            f'Rectangles: {rectangle_count}',
            f'Min Area: {self.min_area}',
            f'Canny: {self.canny_low}-{self.canny_high}',
            'Press Q to quit'
        ]
        
        for i, text in enumerate(info_texts):
            y_pos = 30 + i * 20
            cv2.putText(frame, text, (15, y_pos), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    def create_control_window(self):
        """
        创建参数控制窗口
        """
        cv2.namedWindow('Controls', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Controls', 400, 300)
        
        # 创建滑动条
        cv2.createTrackbar('Min Area/100', 'Controls', 
                          self.min_area//100, 100, self.update_min_area)
        cv2.createTrackbar('Canny Low', 'Controls', 
                          self.canny_low, 200, self.update_canny_low)
        cv2.createTrackbar('Canny High', 'Controls', 
                          self.canny_high, 300, self.update_canny_high)
        cv2.createTrackbar('Epsilon x100', 'Controls', 
                          int(self.epsilon_factor*100), 10, self.update_epsilon)
    
    def update_min_area(self, val):
        self.min_area = val * 100
    
    def update_canny_low(self, val):
        self.canny_low = val
    
    def update_canny_high(self, val):
        self.canny_high = val
    
    def update_epsilon(self, val):
        self.epsilon_factor = val / 100.0
    
    def run(self):
        """
        运行实时检测
        """
        if not self.initialize_camera():
            return
        
        # 创建窗口
        cv2.namedWindow('Camera Rectangle Detection', cv2.WINDOW_NORMAL)
        cv2.namedWindow('Edge Detection', cv2.WINDOW_NORMAL)
        self.create_control_window()
        
        print("实时矩形检测已启动!")
        print("按 'q' 退出程序")
        print("按 's' 保存当前帧")
        print("按 'r' 重置参数")
        
        while True:
            # 读取帧
            ret, frame = self.cap.read()
            if not ret:
                print("无法读取摄像头画面")
                break
            
            # 计算FPS
            self.calculate_fps()
            
            # 每隔几帧处理一次（性能优化）
            self.frame_count += 1
            if self.frame_count % self.frame_skip == 0:
                # 检测矩形
                rectangles, edges = self.detect_rectangles_in_frame(frame)
                
                # 绘制结果
                result_frame = self.draw_rectangles(frame, rectangles)
                
                # 显示边缘检测结果
                cv2.imshow('Edge Detection', edges)
            else:
                result_frame = frame.copy()
                rectangles = []
            
            # 绘制信息覆盖层
            self.draw_info_overlay(result_frame, len(rectangles))
            
            # 显示结果
            cv2.imshow('Camera Rectangle Detection', result_frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 保存当前帧
                filename = f'captured_frame_{int(time.time())}.jpg'
                cv2.imwrite(filename, result_frame)
                print(f"已保存帧: {filename}")
            elif key == ord('r'):
                # 重置参数
                self.reset_parameters()
                print("参数已重置")
        
        # 清理资源
        self.cleanup()
    
    def reset_parameters(self):
        """
        重置检测参数
        """
        self.min_area = 1000
        self.canny_low = 50
        self.canny_high = 150
        self.epsilon_factor = 0.02
        
        # 更新滑动条
        cv2.setTrackbarPos('Min Area/100', 'Controls', self.min_area//100)
        cv2.setTrackbarPos('Canny Low', 'Controls', self.canny_low)
        cv2.setTrackbarPos('Canny High', 'Controls', self.canny_high)
        cv2.setTrackbarPos('Epsilon x100', 'Controls', int(self.epsilon_factor*100))
    
    def cleanup(self):
        """
        清理资源
        """
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print("摄像头已关闭，程序退出")

def main():
    """
    主函数
    """
    print("OpenCV 实时摄像头矩形检测")
    print("=" * 40)
    
    # 选择摄像头
    camera_id = 0
    try:
        camera_input = input(f"请输入摄像头ID (默认: {camera_id}): ").strip()
        if camera_input:
            camera_id = int(camera_input)
    except ValueError:
        print("无效输入，使用默认摄像头ID: 0")
    
    # 创建检测器
    detector = CameraRectangleDetector(camera_id)
    
    # 运行检测
    try:
        detector.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        detector.cleanup()
    except Exception as e:
        print(f"程序出错: {e}")
        detector.cleanup()

if __name__ == "__main__":
    main()

import cv2
import time

def simple_camera_rectangle_detection():
    """
    简单的实时摄像头矩形检测
    """
    print("启动摄像头矩形检测...")
    
    # 初始化摄像头
    cap = cv2.VideoCapture(0)  # 0表示默认摄像头
    
    if not cap.isOpened():
        print("错误: 无法打开摄像头")
        return
    
    # 设置摄像头分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    # FPS计算变量
    fps_counter = 0
    fps_start_time = time.time()
    current_fps = 0
    
    # 检测参数
    min_area = 1000  # 最小矩形面积
    
    print("摄像头已启动!")
    print("操作说明:")
    print("- 按 'q' 退出程序")
    print("- 按 's' 保存当前画面")
    print("- 将矩形物体放在摄像头前进行检测")
    
    while True:
        # 读取摄像头画面
        ret, frame = cap.read()
        if not ret:
            print("无法读取摄像头画面")
            break
        
        # 计算FPS
        fps_counter += 1
        current_time = time.time()
        if current_time - fps_start_time >= 1.0:
            current_fps = fps_counter / (current_time - fps_start_time)
            fps_counter = 0
            fps_start_time = current_time
        
        # 矩形检测处理
        # 1. 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 2. 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 3. 边缘检测
        edges = cv2.Canny(blurred, 50, 150)
        
        # 4. 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 5. 检测矩形
        rectangle_count = 0
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            
            # 过滤太小的轮廓
            if area < min_area:
                continue
            
            # 轮廓近似
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # 检查是否为矩形（4个顶点）
            if len(approx) == 4:
                rectangle_count += 1
                
                # 绘制矩形轮廓
                cv2.drawContours(frame, [approx], -1, (0, 255, 0), 3)
                
                # 获取矩形位置信息
                x, y, w, h = cv2.boundingRect(approx)
                
                # 添加标签
                label = f'矩形 {rectangle_count}'
                cv2.putText(frame, label, (x, y-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                # 显示矩形信息
                info = f'{w}x{h}'
                cv2.putText(frame, info, (x, y+h+25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        # 显示FPS和检测信息
        fps_text = f'FPS: {current_fps:.1f}'
        cv2.putText(frame, fps_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        count_text = f'检测到矩形: {rectangle_count}'
        cv2.putText(frame, count_text, (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 显示操作提示
        cv2.putText(frame, 'Press Q to quit, S to save', (10, frame.shape[0]-20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 显示结果
        cv2.imshow('实时矩形检测', frame)
        cv2.imshow('边缘检测', edges)
        
        # 处理按键
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            # 保存当前画面
            filename = f'screenshot_{int(time.time())}.jpg'
            cv2.imwrite(filename, frame)
            print(f"已保存画面: {filename}")
    
    # 释放资源
    cap.release()
    cv2.destroyAllWindows()
    print("程序已退出")

def test_camera():
    """
    测试摄像头是否可用
    """
    print("测试摄像头...")
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("摄像头不可用，请检查:")
        print("1. 摄像头是否正确连接")
        print("2. 是否有其他程序正在使用摄像头")
        print("3. 摄像头驱动是否正常")
        return False
    
    ret, frame = cap.read()
    if not ret:
        print("无法读取摄像头画面")
        cap.release()
        return False
    
    print(f"摄像头测试成功! 画面尺寸: {frame.shape}")
    cap.release()
    return True

def main():
    """
    主函数
    """
    print("OpenCV 实时摄像头矩形检测 - 简化版")
    print("=" * 50)
    
    # 测试摄像头
    if not test_camera():
        input("按回车键退出...")
        return
    
    print("\n准备启动实时检测...")
    input("按回车键开始...")
    
    try:
        simple_camera_rectangle_detection()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
        print("可能的解决方案:")
        print("1. 确保已安装 opencv-python: pip install opencv-python")
        print("2. 检查摄像头权限设置")
        print("3. 重启程序或重新连接摄像头")

if __name__ == "__main__":
    main()

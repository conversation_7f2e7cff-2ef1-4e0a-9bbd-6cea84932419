import cv2
import numpy as np
import time
import threading
from collections import deque

class AdvancedCameraDetector:
    """
    高性能实时摄像头矩形检测器
    包含多线程处理、性能优化、统计信息等功能
    """
    
    def __init__(self, camera_id=0):
        self.camera_id = camera_id
        self.cap = None
        
        # 检测参数
        self.min_area = 1000
        self.max_area = 100000
        self.epsilon_factor = 0.02
        self.canny_low = 50
        self.canny_high = 150
        
        # 性能优化
        self.process_every_n_frames = 2  # 每N帧处理一次
        self.frame_count = 0
        self.resize_factor = 1.0  # 图像缩放因子
        
        # FPS和统计
        self.fps_queue = deque(maxlen=30)  # 保存最近30帧的时间
        self.detection_times = deque(maxlen=100)  # 检测时间统计
        self.rectangle_history = deque(maxlen=50)  # 矩形数量历史
        
        # 多线程
        self.frame_buffer = None
        self.processed_frame = None
        self.processing_lock = threading.Lock()
        self.is_processing = False
        
        # 显示选项
        self.show_edges = True
        self.show_stats = True
        self.show_history = True
        
    def initialize_camera(self):
        """初始化摄像头"""
        print("初始化摄像头...")
        self.cap = cv2.VideoCapture(self.camera_id)
        
        if not self.cap.isOpened():
            print(f"错误: 无法打开摄像头 {self.camera_id}")
            return False
        
        # 设置摄像头参数
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        self.cap.set(cv2.CAP_PROP_FPS, 30)
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲延迟
        
        # 获取实际参数
        width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = self.cap.get(cv2.CAP_PROP_FPS)
        
        print(f"摄像头设置: {width}x{height} @ {fps}fps")
        return True
    
    def calculate_fps(self):
        """计算实时FPS"""
        current_time = time.time()
        self.fps_queue.append(current_time)
        
        if len(self.fps_queue) >= 2:
            time_diff = self.fps_queue[-1] - self.fps_queue[0]
            if time_diff > 0:
                return (len(self.fps_queue) - 1) / time_diff
        return 0
    
    def detect_rectangles_optimized(self, frame):
        """优化的矩形检测"""
        start_time = time.time()
        
        # 图像预处理
        if self.resize_factor != 1.0:
            height, width = frame.shape[:2]
            new_width = int(width * self.resize_factor)
            new_height = int(height * self.resize_factor)
            frame = cv2.resize(frame, (new_width, new_height))
        
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 自适应直方图均衡化（改善光照不均）
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        gray = clahe.apply(gray)
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 边缘检测
        edges = cv2.Canny(blurred, self.canny_low, self.canny_high)
        
        # 形态学操作（连接断开的边缘）
        kernel = np.ones((3,3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 检测矩形
        rectangles = []
        for contour in contours:
            area = cv2.contourArea(contour)
            
            if self.min_area <= area <= self.max_area:
                # 轮廓近似
                epsilon = self.epsilon_factor * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                if len(approx) == 4:
                    # 额外的矩形验证
                    if self.is_valid_rectangle(approx):
                        rectangles.append(approx)
        
        # 记录检测时间
        detection_time = time.time() - start_time
        self.detection_times.append(detection_time)
        
        return rectangles, edges
    
    def is_valid_rectangle(self, approx):
        """验证是否为有效矩形"""
        # 计算角度
        def angle_between_vectors(v1, v2):
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            return np.arccos(np.clip(cos_angle, -1.0, 1.0)) * 180 / np.pi
        
        points = approx.reshape(4, 2)
        
        # 计算相邻边的角度
        angles = []
        for i in range(4):
            p1 = points[i]
            p2 = points[(i + 1) % 4]
            p3 = points[(i + 2) % 4]
            
            v1 = p1 - p2
            v2 = p3 - p2
            angle = angle_between_vectors(v1, v2)
            angles.append(angle)
        
        # 检查角度是否接近90度
        for angle in angles:
            if not (70 <= angle <= 110):  # 允许20度误差
                return False
        
        return True
    
    def draw_enhanced_rectangles(self, frame, rectangles):
        """绘制增强的矩形显示"""
        result = frame.copy()
        
        for i, rect in enumerate(rectangles):
            # 绘制矩形轮廓
            cv2.drawContours(result, [rect], -1, (0, 255, 0), 2)
            
            # 绘制顶点
            for point in rect:
                cv2.circle(result, tuple(point[0]), 5, (255, 0, 0), -1)
            
            # 获取矩形信息
            x, y, w, h = cv2.boundingRect(rect)
            area = cv2.contourArea(rect)
            
            # 计算长宽比
            aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else 0
            
            # 标签信息
            label = f'R{i+1}'
            info = f'{w}x{h} ({area:.0f})'
            ratio_info = f'Ratio: {aspect_ratio:.1f}'
            
            # 绘制标签
            cv2.putText(result, label, (x, y-30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(result, info, (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            cv2.putText(result, ratio_info, (x, y+h+15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
        
        return result
    
    def draw_statistics_overlay(self, frame, rectangle_count):
        """绘制统计信息覆盖层"""
        if not self.show_stats:
            return
        
        height, width = frame.shape[:2]
        
        # 计算统计信息
        current_fps = self.calculate_fps()
        avg_detection_time = np.mean(self.detection_times) * 1000 if self.detection_times else 0
        avg_rectangles = np.mean(self.rectangle_history) if self.rectangle_history else 0
        
        # 创建信息面板
        panel_height = 150
        panel = np.zeros((panel_height, 350, 3), dtype=np.uint8)
        panel[:] = (40, 40, 40)  # 深灰色背景
        
        # 统计信息文本
        stats = [
            f'FPS: {current_fps:.1f}',
            f'Detection Time: {avg_detection_time:.1f}ms',
            f'Current Rectangles: {rectangle_count}',
            f'Average Rectangles: {avg_rectangles:.1f}',
            f'Frame: {self.frame_count}',
            f'Process Every: {self.process_every_n_frames} frames'
        ]
        
        # 绘制统计信息
        for i, stat in enumerate(stats):
            y_pos = 25 + i * 20
            cv2.putText(panel, stat, (10, y_pos), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 将面板叠加到主画面
        frame[10:10+panel_height, width-360:width-10] = panel
    
    def draw_history_graph(self, frame):
        """绘制矩形数量历史图表"""
        if not self.show_history or len(self.rectangle_history) < 2:
            return
        
        height, width = frame.shape[:2]
        graph_width = 200
        graph_height = 100
        graph_x = width - graph_width - 20
        graph_y = height - graph_height - 20
        
        # 创建图表背景
        cv2.rectangle(frame, (graph_x, graph_y), 
                     (graph_x + graph_width, graph_y + graph_height), 
                     (40, 40, 40), -1)
        cv2.rectangle(frame, (graph_x, graph_y), 
                     (graph_x + graph_width, graph_y + graph_height), 
                     (255, 255, 255), 1)
        
        # 绘制历史数据
        if len(self.rectangle_history) > 1:
            max_val = max(self.rectangle_history) if max(self.rectangle_history) > 0 else 1
            points = []
            
            for i, val in enumerate(self.rectangle_history):
                x = graph_x + int(i * graph_width / len(self.rectangle_history))
                y = graph_y + graph_height - int(val * graph_height / max_val)
                points.append((x, y))
            
            # 绘制折线图
            for i in range(len(points) - 1):
                cv2.line(frame, points[i], points[i + 1], (0, 255, 255), 2)
        
        # 标题
        cv2.putText(frame, 'Rectangle Count History', 
                   (graph_x, graph_y - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    def process_frame_thread(self, frame):
        """在单独线程中处理帧"""
        with self.processing_lock:
            if not self.is_processing:
                self.is_processing = True
                rectangles, edges = self.detect_rectangles_optimized(frame)
                self.processed_frame = (rectangles, edges)
                self.is_processing = False
    
    def run(self):
        """运行检测器"""
        if not self.initialize_camera():
            return
        
        # 创建窗口
        cv2.namedWindow('Advanced Rectangle Detection', cv2.WINDOW_NORMAL)
        if self.show_edges:
            cv2.namedWindow('Edge Detection', cv2.WINDOW_NORMAL)
        
        print("高级矩形检测器已启动!")
        print("快捷键:")
        print("  q - 退出")
        print("  s - 保存当前帧")
        print("  e - 切换边缘检测显示")
        print("  t - 切换统计信息显示")
        print("  h - 切换历史图表显示")
        print("  + - 增加处理频率")
        print("  - - 减少处理频率")
        
        rectangles = []
        edges = None
        
        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    break
                
                self.frame_count += 1
                
                # 处理帧（每N帧处理一次）
                if self.frame_count % self.process_every_n_frames == 0:
                    # 使用多线程处理
                    thread = threading.Thread(target=self.process_frame_thread, args=(frame,))
                    thread.start()
                
                # 获取处理结果
                with self.processing_lock:
                    if self.processed_frame is not None:
                        rectangles, edges = self.processed_frame
                        self.processed_frame = None
                
                # 记录矩形数量历史
                self.rectangle_history.append(len(rectangles))
                
                # 绘制结果
                result_frame = self.draw_enhanced_rectangles(frame, rectangles)
                self.draw_statistics_overlay(result_frame, len(rectangles))
                self.draw_history_graph(result_frame)
                
                # 显示结果
                cv2.imshow('Advanced Rectangle Detection', result_frame)
                
                if self.show_edges and edges is not None:
                    cv2.imshow('Edge Detection', edges)
                
                # 处理按键
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    filename = f'advanced_capture_{int(time.time())}.jpg'
                    cv2.imwrite(filename, result_frame)
                    print(f"已保存: {filename}")
                elif key == ord('e'):
                    self.show_edges = not self.show_edges
                    if not self.show_edges:
                        cv2.destroyWindow('Edge Detection')
                    else:
                        cv2.namedWindow('Edge Detection', cv2.WINDOW_NORMAL)
                elif key == ord('t'):
                    self.show_stats = not self.show_stats
                elif key == ord('h'):
                    self.show_history = not self.show_history
                elif key == ord('+') or key == ord('='):
                    self.process_every_n_frames = max(1, self.process_every_n_frames - 1)
                    print(f"处理频率: 每 {self.process_every_n_frames} 帧")
                elif key == ord('-'):
                    self.process_every_n_frames = min(10, self.process_every_n_frames + 1)
                    print(f"处理频率: 每 {self.process_every_n_frames} 帧")
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print("高级检测器已关闭")

def main():
    detector = AdvancedCameraDetector()
    detector.run()

if __name__ == "__main__":
    main()
